﻿<Page x:Class="WPF_PCV.Views.Pages.Comparison"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:WPF_PCV.Views.Pages"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="1000"
      Title="Comparison">

    <Grid ShowGridLines="False">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 中间区域，使用 Grid 三行三列布局 -->
        <Grid Grid.Row="1">
            <!-- 三行：文件行、目录行、进度行 -->
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <!-- 三列：标签（Auto）、输入区（*）、按钮（Auto） -->
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 第一行：截面带数据文件 -->
            <TextBlock 
                Grid.Row="0" Grid.Column="0"
                Text="截面带数据文件："
                VerticalAlignment="Center"
                HorizontalAlignment="Center"
                FontWeight="Bold"
                Margin="10,5"/>
            <TextBox 
                Grid.Row="0" Grid.Column="1"
                x:Name="FilePath" Height="30"
                IsReadOnly="True"
                Text="C:\AAA-personalData\project\00-graduateProject\001-Laser_Profile\WPF_PCV\pyFunc\Comparison\Data\250606\20250606103010____ProcsData.txt"
                VerticalAlignment="Center"
                VerticalContentAlignment="Center"/>
            <Button 
                Grid.Row="0" Grid.Column="2"
                x:Name="ChooseFile"
                Click="ChooseFile_Click"
                Content="选择文件" Height="30"
                Margin="10,5"/>

            <!-- 第二行：CMM 目录 -->
            <TextBlock 
                Grid.Row="1" Grid.Column="0"
                Text="CMM 目录："
                VerticalAlignment="Center"
                HorizontalAlignment="Center"
                FontWeight="Bold"
                Margin="10,5"/>
            <TextBox 
                Grid.Row="1" Grid.Column="1"
                x:Name="FolderPath" Height="30"
                IsReadOnly="True"
                Text="C:\AAA-personalData\project\00-graduateProject\001-Laser_Profile\WPF_PCV\pyFunc\Comparison\Data\cmm0609"
                VerticalAlignment="Center"
                VerticalContentAlignment="Center"/>
            <Button 
                Grid.Row="1" Grid.Column="2"
                x:Name="ChooseFolder"
                Click="ChooseFolder_Click"
                Content="选择CMM文件夹" Height="30"
                Margin="10,5"/>

            <!-- 第三行：进度 -->
            <TextBlock 
                Grid.Row="2" Grid.Column="0"
                Text="进度："
                VerticalAlignment="Center"
                HorizontalAlignment="Center"
                FontWeight="Bold"
                Margin="10,5"/>
            <ProgressBar 
                Grid.Row="2" Grid.Column="1"
                x:Name="PbReportProgress"
                Height="30"
                Minimum="0" Maximum="100" Value="0"
                VerticalAlignment="Center"/>
            <Button 
                Grid.Row="2" Grid.Column="2"
                x:Name="OutputPDF"
                Click="OutputPDF_Click"
                Content="一键生成PDF报告" Height="30"
                Margin="10,5"/>
        </Grid>
        <!-- 底部日志框 -->
        <TextBox
            Grid.Row="2"
            x:Name="TbProgressInfo"
            Height="200"
            Margin="10,5,10,0"
            Text="等待生成…"
            TextWrapping="Wrap"/>
    </Grid>
</Page>