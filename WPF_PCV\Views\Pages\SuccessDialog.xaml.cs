﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Diagnostics;
using System.IO;

namespace WPF_PCV.Views.Pages
{
    public partial class SuccessDialog : Window
    {
        private readonly string _pdfPath;
        private readonly string _folderPath;
        public SuccessDialog(string info, string pdfPath, string folderPath)
        {
            InitializeComponent();
            TbInfo.Text = info;
            _pdfPath = pdfPath;
            _folderPath = folderPath;
            BtnViewPdf.IsEnabled = !string.IsNullOrEmpty(pdfPath) && File.Exists(pdfPath);
        }

        private void BtnViewPdf_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(_pdfPath) && File.Exists(_pdfPath))
                {
                    Process.Start(new ProcessStartInfo(_pdfPath) { UseShellExecute = true });
                }
                else
                {
                    MessageBox.Show("未找到PDF文件！", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开PDF失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void BtnOpenFolder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(_folderPath))
                {
                    Process.Start(new ProcessStartInfo(_folderPath) { UseShellExecute = true });
                }
                else
                {
                    MessageBox.Show("未找到文件夹！", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开文件夹失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
