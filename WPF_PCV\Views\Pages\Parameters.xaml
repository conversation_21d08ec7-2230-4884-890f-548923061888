﻿<Page x:Class="WPF_PCV.Views.Pages.Parameters"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:WPF_PCV.Views.Pages"
      xmlns:hx="http://helix-toolkit.org/wpf/SharpDX"
      xmlns:oxy="http://oxyplot.org/wpf"
      xmlns:xctk="http://schemas.xceed.com/wpf/xaml/toolkit"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="1000"
      Title="Parameters">

    <Grid ShowGridLines="False">
        <!-- Define rows: header and content -->
        <Grid.RowDefinitions>
            <RowDefinition Height="25"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <!-- Define three columns -->
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="1*"/>
        </Grid.ColumnDefinitions>

        <!-- 顶部分区标题 -->
        <Border Grid.Row="0" Grid.Column="0" Background="#AAAAAA" Margin="5,0,0,0">
            <TextBlock Text="三维显示" VerticalAlignment="Center" HorizontalAlignment="Center" FontWeight="Bold"/>
        </Border>
        <Border Grid.Row="0" Grid.Column="1" Background="#AAAAAA">
            <TextBlock Text="截面显示" VerticalAlignment="Center" HorizontalAlignment="Center" FontWeight="Bold"/>
        </Border>
        <Border Grid.Row="0" Grid.Column="2" Background="#AAAAAA" Margin="0,0,5,0">
            <TextBlock Text="分析及报告" VerticalAlignment="Center" HorizontalAlignment="Center" FontWeight="Bold"/>
        </Border>

        <!-- Left Panel: 3D Display -->
        <Grid Grid.Row="1" Grid.Column="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <!-- 3D Viewport using SharpDX -->
            <hx:Viewport3DX x:Name="helixViewport"
                        Grid.Row="0"
                Background="WhiteSmoke"
                CameraMode="Inspect"
                ZoomExtentsWhenLoaded="True"
                ShowViewCube="True"
                CameraRotationMode="Trackball"
                ZoomRectangleCursor="ScrollSE"
                ZoomCursor="SizeNS"
                RotateCursor="SizeAll"
                PanCursor="Hand"
                ChangeFieldOfViewCursor="ScrollNS"
                ShowCoordinateSystem="True"
                BorderThickness="1"
                BorderBrush="Gray"
                Margin="5"
                EnableSwapChainRendering="True"
                EnableSSAO="False"
                MSAA="Two">

                <!-- Effects Manager -->
                <hx:Viewport3DX.EffectsManager>
                    <hx:DefaultEffectsManager />
                </hx:Viewport3DX.EffectsManager>

                <!-- Camera Configuration -->
                <hx:Viewport3DX.Camera>
                    <hx:OrthographicCamera x:Name="MainCamera"
                    UpDirection="0 0 1"
                    NearPlaneDistance="0.1"
                    FarPlaneDistance="100000"/>
                </hx:Viewport3DX.Camera>

                <!-- Lighting Setup -->
                <hx:AmbientLight3D Color="#444444" />
                <hx:DirectionalLight3D Direction="-1,-1,-1" Color="White" />
                <hx:DirectionalLight3D Direction="1,1,1" Color="#CCCCCC" />

            </hx:Viewport3DX>
            <!-- Bottom toolbar -->
            <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,2">
                <TextBlock Text="降采样显示" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <xctk:IntegerUpDown x:Name="DownsampleLevelUpDown" Value="1" Minimum="1" Maximum="10" Width="50" HorizontalAlignment="Center" ValueChanged="DownsampleLevelUpDown_ValueChanged"/>
                <CheckBox Content="新数据不画图和计算" Margin="20,0,0,0" VerticalAlignment="Center"/>
            </StackPanel>
        </Grid>

        <!-- Center Panel: Cross-section Display -->
        <Grid Grid.Row="1" Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <!-- Radio buttons and controls -->
            <WrapPanel Orientation="Horizontal" HorizontalAlignment="Center" Background="White" Margin="0,5,0,0">
                <RadioButton Content="前缘半径" GroupName="Section" IsChecked="True" Margin="0,3,3,0"/>
                <RadioButton Content="后缘半径" GroupName="Section" Margin="0,3,3,0"/>
                <RadioButton Content="弦长" GroupName="Section" Margin="0,3,3,0"/>
                <RadioButton Content="中弦线" GroupName="Section" Margin="0,3,10,0"/>
                <Button Content="轮廓数据" Margin="0,0,0,0"/>
                <RadioButton Content="最大厚度" GroupName="Section" Margin="0,3,3,0"/>
                <RadioButton Content="前缘厚度" GroupName="Section" Margin="0,3,3,0"/>
                <RadioButton Content="后缘厚度" GroupName="Section" Margin="0,3,3,0"/>
                <RadioButton Content="极值" GroupName="Section" Margin="0,3,3,0"/>
                <RadioButton Content="杯突" GroupName="Section" Margin="0,3,0,0"/>
            </WrapPanel>

            <Border Grid.Row="1">
                <DockPanel>
                    <!-- 按钮区 -->
                    <StackPanel DockPanel.Dock="Bottom" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,3">
                        <Button x:Name="btnPrevSlice" Content="上一层" Click="BtnPrevSlice_Click"/>
                        <TextBlock x:Name="txtSliceInfo" VerticalAlignment="Center" TextWrapping="Wrap" FontSize="12" Margin="5,0"/>
                        <Button x:Name="btnNextSlice" Content="下一层" Click="BtnNextSlice_Click"/>
                    </StackPanel>
                    <oxy:PlotView x:Name="PlotView" Margin="0,5,0,5" BorderBrush="DarkGray" BorderThickness="1"/>
                </DockPanel>
            </Border>
        </Grid>

        <!-- 第三列: 分析及报告 -->
        <Grid Grid.Row="1" Grid.Column="2" Margin="5">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <!-- 文件选择 + 分析按钮 -->
            <DockPanel Grid.Row="0" LastChildFill="True" Margin="0,0,0,5">
                <TextBox x:Name="txtFilePath"
                         VerticalAlignment="Center"
                         DockPanel.Dock="Left"
                         Width="200"
                         TextWrapping="Wrap" />
                <Button x:Name="btnOpenFile"
                        Content="选择文件"
                        VerticalAlignment="Center"
                        Click="BtnOpenFile_Click"
                        Margin="5,0,0,0"
                        Width="62"/>
                <Button x:Name="AnalyzeButton"
                        Content="一键分析"
                        VerticalAlignment="Center"
                        Margin="5,0,0,0"/>
            </DockPanel>

            <!-- 中间列表区 -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="3*" />
                    <ColumnDefinition Width="5*" />
                </Grid.ColumnDefinitions>
                <!-- 截面选择 -->
                <Border BorderThickness="0.8" BorderBrush="gray">
                    <Grid Grid.Column="0" Background="WhiteSmoke">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <TextBlock
                            Text="截面选择(mm)"
                            Margin="0,4,0,5"
                            HorizontalAlignment="Center"/>
                        <ListBox
                            x:Name="SectionListBox"
                            Grid.Row="1"
                            BorderThickness="0"
                            SelectionMode="Single"/>
                    </Grid>
                </Border>

                <!-- 特征参数 套表 -->
                <DataGrid Grid.Column="1" x:Name="dgParameters" AutoGenerateColumns="False" SelectionMode="Single" IsReadOnly="True" Margin="5,0,0,0" BorderThickness="1" BorderBrush="gray">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="特征参数名称" FontWeight="Bold" Binding="{Binding Id}" Width="1*"/>
                        <DataGridTextColumn Header="特征值(mm)" Binding="{Binding Name}" Width="1*"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>

            <TextBlock Grid.Row="2" HorizontalAlignment="Center" x:Name="txtStatus" VerticalAlignment="Center" Foreground="Black" FontSize="12" Margin="0,7,0,0" TextWrapping="Wrap"/>
        </Grid>
    </Grid>
</Page>