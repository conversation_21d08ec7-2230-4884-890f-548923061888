<Page x:Class="WPF_PCV.Views.Pages.ScanPath"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:WPF_PCV.Views.Pages"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="ScanPath">

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="210"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 输入参数 -->
        <TextBlock Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" Margin="5" FontSize="15">L (通道面长度 mm):</TextBlock>
        <TextBox x:Name="TxtL" Grid.Row="0" Grid.Column="1" Margin="5" FontSize="15"/>

        <TextBlock Grid.Row="1" Grid.Column="0" VerticalAlignment="Center" Margin="5" FontSize="15">W1 (叶片缘板最大宽度 mm):</TextBlock>
        <TextBox x:Name="TxtW1" Grid.Row="1" Grid.Column="1" Margin="5" FontSize="15"/>

        <TextBlock Grid.Row="2" Grid.Column="0" VerticalAlignment="Center" Margin="5" FontSize="15">W2 (叶冠最大宽度 mm):</TextBlock>
        <TextBox x:Name="TxtW2" Grid.Row="2" Grid.Column="1" Margin="5" FontSize="15"/>

        <Button x:Name="BtnGenerate" Grid.Row="3" Grid.Column="1" Content="生成路径" Width="100" HorizontalAlignment="Right" Margin="5" Click="BtnGenerate_Click" FontSize="15"/>

        <!-- 输出区域 -->
        <TextBox x:Name="TxtOutput" Grid.Row="5" Grid.ColumnSpan="2" Margin="5" AcceptsReturn="True"
                 VerticalScrollBarVisibility="Auto" TextWrapping="Wrap" FontFamily="Consolas" FontSize="15"/>
    </Grid>
</Page>
