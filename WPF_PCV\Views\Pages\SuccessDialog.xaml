﻿<Window x:Class="WPF_PCV.Views.Pages.SuccessDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WPF_PCV.Views.Pages"
        mc:Ignorable="d"
        Title="SuccessDialog" Height="170" Width="285">
    <Grid Margin="10,10,10,10">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <TextBlock x:Name="TbInfo" Grid.Row="0" TextWrapping="Wrap"/>
        <Grid Grid.Row="1" HorizontalAlignment="Center">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Button x:Name="BtnOpenFolder" Content="打开文件夹" Width="70" Height="25" Grid.Column="0" Click="BtnOpenFolder_Click" Margin="0,0,10,0"/>
            <Button x:Name="BtnViewPdf" Content="查看PDF" Width="70" Height="25" Grid.Column="1" Click="BtnViewPdf_Click" Margin="0,0,10,0"/>
            <Button Content="关闭弹窗" Width="70" Height="25" Grid.Column="2" Click="BtnClose_Click"/>
        </Grid>
    </Grid>
</Window>
