# README.md

[English](#) | [简体中文](./README_CN.md)

---

A desktop application developed using WPF (Windows Presentation Foundation), supporting point cloud loading, display, sampling, and slice visualization. The project integrates graphics visualization components such as [HelixToolkit.Wpf.SharpDX](https://github.com/helix-toolkit/helix-toolkit) and [OxyPlot](https://oxyplot.github.io/), suitable for scenarios like 3D graphics processing, computer vision, engineering education, and scientific research.

Runtime demo:
![](./Assets/Images/show.gif)

## 📦 Features

* ✅ Load local `.txt` point cloud data
* ✅ Support for point cloud downsampling (voxel grid, random...)
* ✅ Support 2D and 3D point cloud visualization
* ✅ Support slice visualization (based on projection planes)
* ✅ 3D interactive display using HelixToolkit
* ✅ Display statistical curves and slice plots using OxyPlot

## 🛠️ Tech Stack

* Framework: WPF (.NET Framework 4.8)
* Rendering: HelixToolkit.Wpf.SharpDX, OxyPlot.Wpf
* Language: C#
* Project Management: Visual Studio 2022

## 📁 Project Structure

```markdown
WPF_PCV/
│  App.xaml             # Application entry point  
│  index.xaml           # Main window UI  
│  index.xaml.cs        # Main window logic  
│  packages.config      # NuGet package configuration  
│  WPF_PCV.csproj       # Project file  
│
├─Properties/           # Project property settings  
├─data4test/            # Sample data folder (reduce.txt...)  
└─...                   # Other code and resource files  
```

## 🧰 Environment Requirements

Please ensure the following environments are installed:

* ✅ Visual Studio 2022 (with .NET Desktop Development workload)
* ✅ .NET Framework 4.8 Developer Pack
* ✅ NuGet package support (VS will auto-restore packages)

## 🚀 Getting Started

```shell
# Clone the project
git clone https://github.com/get1024/WPF_PCV.git
cd WPF_PCV

# Open WPF_PCV.sln in Visual Studio
# VS will automatically restore NuGet packages defined in packages.config

# Build & Run (press F5)
```

## 📝 Data Format

The point cloud data is stored in `.txt` files, each line representing a 3D point with the format:

```
-0.053	-8.215	50.000
-0.062	-8.199	50.000
-0.078	-8.174	50.000
-0.087	-8.159	35.000
-0.101	-8.136	35.000
-0.109	-8.121	35.000
-0.113	-8.113	35.000
...
```

## 💡 TODO

* [ ] Downsampling algorithm needs improvement; current results are not ideal
* [ ] Layering algorithm needs refinement to fix occasional layer sticking
* [ ] Display point cloud with real-world coordinates
* ……

## 📄 License

This project currently does not specify an open-source license (default is All Rights Reserved). For commercial use or secondary development, please contact the author.

## ✨ Author

* 👤 [RyanJoy](https://github.com/get1024)