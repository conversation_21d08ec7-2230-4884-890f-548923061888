@startuml WPF_PCV
'--------------------
' 通用布局设置
'--------------------
skinparam classAttributeIconSize 0
skinparam shadowing true
skinparam linetype ortho
skinparam dpi 150
skinparam class {
    BackgroundColor White
    ArrowColor Black
    BorderColor Black
}

'--------------------
' 核心模型 (Core Models)
'--------------------
package "Core Models" {
    class PointCloudData {
        +List<Point3D> Points
        +DateTime Timestamp
    }
    class CalibrationResult {
        +Vector3 Offset
        +Quaternion Orientation
    }
    class ExtractionParameters {
        +double Diameter
        +double Roundness
    }
    class AnalysisResult {
        +string Summary
        +Dictionary<string,double> Metrics
    }
    class FileRepository {
        +void SaveRaw(string path, PointCloudData data)
        +PointCloudData LoadRaw(string path)
        +void SaveParameters(string path, ExtractionParameters p)
    }
}

'--------------------
' ViewModel 层 (ViewModels)
'--------------------
package "ViewModels" {
    class MainWindowViewModel {
        -IInitializationService initSvc
        -IScannerService scannerSvc
        -IFilterService filterSvc
        -IMergerService mergerSvc
        -IDeduplicationService dedupSvc
        -IAnalysisService analysisSvc
        -IParameterExtractionService extractSvc
        -ICalibrationService calibSvc
        -IValidationService validSvc
        -INavigationService navSvc
        -FileRepository repo

        +ICommand ScanCommand
        +ICommand AnalyzeCommand
        +ICommand CalibrateCommand
        +ICommand ValidateCommand
        +ICommand NavigateCommand

        +ObservableCollection<PointCloudData> LiveData
        +ExtractionParameters CurrentParameters
        +CalibrationResult CurrentCalibration
        +CalibrationResult CurrentValidation
        +AnalysisResult CurrentAnalysis

        +void OnScan()
        +void OnAnalyze()
        +void OnCalibrate()
        +void OnValidate()
        +void OnNavigate(string pageKey)
    }
    note left:这里没有把所有的Service都用ICommand转发，\n主要就设置了核心的几个（扫描、分析、标定、\n验证、导航），其余的（滤波、去重、拼合、参\n数提取）当下都默认是【核心业务】背后执行时\n会调用的子业务，所以并不需要提供ICommand\n暴露。\n如果后续真的需要添加，我们再补充即可。

    class RealTimeDisplayViewModel {
        +ObservableCollection<PointCloudData> DisplayQueue
        +void Enqueue(PointCloudData data)
    }

    class EventTriggerViewModel {
        -INavigationService navSvc
        +ICommand OneClickScan
        +ICommand OneClickAnalyze
        +ICommand OneClickCalibrate
        +ICommand OneClickValidate
        +ICommand OneClickNavigate
    }
    class NavigationViewModel {
        -INavigationService navSvc
        +void GoToCalibration()
        +void GoToValidation()
        +void GoToLiveView()
    }
}

'--------------------
' 服务层 (Services)
'--------------------
package "Services" {
    interface IInitializationService {
        +void InitializeThreads()
        +void InitializeHardware()
    }
    interface IScannerService {
        +PointCloudData ReadNextBatch()
    }
    interface IFilterService {
        +PointCloudData Filter(PointCloudData raw)
    }
    interface IMergerService {
        +PointCloudData Merge(IEnumerable<PointCloudData> list)
    }
    interface IDeduplicationService {
        +PointCloudData Deduplicate(PointCloudData merged)
    }
    interface IAnalysisService {
        +AnalysisResult Analyze(string scriptPath, string inputDataPath)
    }
    interface IParameterExtractionService {
        +ExtractionParameters Extract(PointCloudData data)
    }
    interface ICalibrationService {
        +CalibrationResult Calibrate(PointCloudData data)
    }
    interface IValidationService {
        +CalibrationResult Validate(PointCloudData data)
    }
    interface INavigationService {
        +void Navigate(string pageKey)
    }
    interface IPageService {
        +Type GetPageType(string pageKey)
    }
    interface INavigationService
    interface IPageService
}

'--------------------
' 依赖关系 & 组合
'--------------------
' ViewModel → Services
MainWindowViewModel *-- IInitializationService
MainWindowViewModel *-- IScannerService
MainWindowViewModel *-- IFilterService
MainWindowViewModel *-- IMergerService
MainWindowViewModel *-- IDeduplicationService
MainWindowViewModel *-- IAnalysisService
MainWindowViewModel *-- IParameterExtractionService
MainWindowViewModel *-- ICalibrationService
MainWindowViewModel *-- IValidationService
MainWindowViewModel *-- INavigationService
MainWindowViewModel *-- FileRepository
EventTriggerViewModel *-- INavigationService

' ViewModels 关联
MainWindowViewModel o-- RealTimeDisplayViewModel
MainWindowViewModel o-- EventTriggerViewModel
MainWindowViewModel o-- NavigationViewModel

' Services → Models
IScannerService ..> PointCloudData
IFilterService ..> PointCloudData
IMergerService ..> PointCloudData
IDeduplicationService ..> PointCloudData
IAnalysisService ..> AnalysisResult
IParameterExtractionService ..> ExtractionParameters
ICalibrationService ..> CalibrationResult
IValidationService ..> CalibrationResult
FileRepository ..> PointCloudData
FileRepository ..> ExtractionParameters

' Navigation wiring
INavigationService ..> IPageService
NavigationViewModel *-- INavigationService

IPageService .[hidden] INavigationService
EventTriggerViewModel ..[hidden] NavigationViewModel

@enduml