using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace WPF_PCV.Views.Pages
{
    /// <summary>
    /// ScanPath.xaml 的交互逻辑
    /// </summary>
    public partial class ScanPath : Page
    {
        // 已知激光头参数
        private const double h1 = 51.5;    // 喷嘴下缘到参考面的高度(mm)
        private const double w1 = 33.2;    // h1处的光带宽度(mm)
        private const double h2 = 94.5;    // 第二参考高度(mm)
        private const double w2 = 42.6;    // h2处的光带宽度(mm)
        private readonly double alpha;     // 半发散角

        public ScanPath()
        {
            InitializeComponent();
            // 计算半发散角 alpha
            double dh = h2 - h1;
            double dw = w2 - w1;
            alpha = Math.Atan((dw / 2) / dh);
        }

        private void BtnGenerate_Click(object sender, RoutedEventArgs e)
        {
            // 读取用户输入
            if (!double.TryParse(TxtL.Text, out double L) ||
                !double.TryParse(TxtW1.Text, out double W1) ||
                !double.TryParse(TxtW2.Text, out double W2))
            {
                MessageBox.Show("请输入有效的数字参数。", "参数错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // 求解 H 和 theta（B轴角度）
            // 方程1: W1 = 2*(H - h1)*tan(alpha)
            // 方程2: W2 = (H - h1)*(tan(alpha+theta) + tan(alpha-theta))

            // 先解 H: H = W1/(2*tan(alpha)) + h1
            double H = W1 / (2 * Math.Tan(alpha)) + h1;

            // 再用二分法求 theta
            double theta = SolveTheta(W2, H);
            double thetaDeg = theta * 180.0 / Math.PI;

            // 生成路径文本
            var sb = new StringBuilder();
            sb.AppendLine("; 生成时间: " + DateTime.Now);
            sb.AppendLine($"; 计算结果: H = {H:F2} mm, θ = {thetaDeg:F2}°");
            sb.AppendLine();

            // 正面扫描
            sb.AppendLines(MakeSeg(0, 0, L, 0, 0, 0, H));
            // 侧面扫描 (B=theta)
            sb.AppendLines(MakeSeg(L, 0, L, W2, 0, thetaDeg, H));
            // A翻转180°后再扫背面
            sb.AppendLines(MakeSeg(L, W2, 0, W2, 180, thetaDeg, H));
            sb.AppendLines(MakeSeg(0, W2, 0, 0, 180, 0, H));

            TxtOutput.Text = sb.ToString();
        }

        // 二分法在[-π/2, π/2]范围内求解θ
        private double SolveTheta(double targetW, double H)
        {
            double low = -Math.PI / 2 + 1e-3;
            double high = Math.PI / 2 - 1e-3;
            for (int i = 0; i < 50; i++)
            {
                double mid = (low + high) / 2;
                double w = ProjectedWidth(H, mid);
                if (w > targetW) high = mid;
                else low = mid;
            }
            return (low + high) / 2;
        }

        // 根据H和θ计算投影宽度
        private double ProjectedWidth(double H, double theta)
        {
            double dh = H - h1;
            double t1 = Math.Tan(alpha + theta);
            double t2 = Math.Tan(alpha - theta);
            return dh * (t1 + t2);
        }

        // 生成MOV/SCN段
        private string[] MakeSeg(double x0, double y0, double x1, double y1, double A, double B, double H)
        {
            return new[]
            {
                $"MOV X{x0:F2} Y{y0:F2} Z{H:F2} A{A:F1} B{B:F1}",
                $"SCN X{x1:F2} Y{y1:F2} Z{H:F2} A{A:F1} B{B:F1}"
            };
        }
    }

    // 在 ScanPath 类中添加如下扩展方法
    public static class StringBuilderExtensions
    {
        public static StringBuilder AppendLines(this StringBuilder sb, IEnumerable<string> lines)
        {
            foreach (var line in lines)
            {
                sb.AppendLine(line);
            }
            return sb;
        }
    }
}