using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace WPF_PCV.Views.Pages
{
    /// <summary>
    /// ScanPath.xaml 的交互逻辑
    /// </summary>
    public partial class ScanPath : Page
    {
        // 叶片尺寸分类枚举
        public enum BladeSize
        {
            Small,
            Medium,
            Large
        }

        public ScanPath()
        {
            InitializeComponent();
        }

        private void BtnGenerate_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 读取用户输入
                if (!double.TryParse(TxtL.Text, out double L) ||
                    !double.TryParse(TxtW1.Text, out double W1) ||
                    !double.TryParse(TxtW2.Text, out double W2))
                {
                    MessageBox.Show("请输入有效的数字参数。", "参数错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 验证输入参数的有效性
                if (L <= 0 || W1 <= 0 || W2 <= 0)
                {
                    MessageBox.Show("所有参数必须大于0。", "参数错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 根据输入参数分类叶片尺寸
                BladeSize bladeSize = ClassifyBladeSize(L, W1, W2);

                // 获取对应的路径文件并读取内容
                string pathContent = ReadPathFile(bladeSize);

                // 显示结果
                DisplayResult(L, W1, W2, bladeSize, pathContent);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理过程中发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 根据输入参数分类叶片尺寸
        /// </summary>
        /// <param name="L">通道面长度 (mm)</param>
        /// <param name="W1">叶片缘板最大宽度 (mm)</param>
        /// <param name="W2">叶冠最大宽度 (mm)</param>
        /// <returns>叶片尺寸分类</returns>
        private BladeSize ClassifyBladeSize(double L, double W1, double W2)
        {
            // 计算综合尺寸指标
            // 这里使用一个简单的综合评分算法，您可以根据实际需求调整分类逻辑
            double sizeScore = CalculateSizeScore(L, W1, W2);

            // 根据综合评分进行分类
            // 您可以根据实际的叶片规格调整这些阈值
            if (sizeScore < 100)
            {
                return BladeSize.Small;
            }
            else if (sizeScore < 200)
            {
                return BladeSize.Medium;
            }
            else
            {
                return BladeSize.Large;
            }
        }

        /// <summary>
        /// 计算叶片尺寸综合评分
        /// </summary>
        /// <param name="L">通道面长度 (mm)</param>
        /// <param name="W1">叶片缘板最大宽度 (mm)</param>
        /// <param name="W2">叶冠最大宽度 (mm)</param>
        /// <returns>综合评分</returns>
        private double CalculateSizeScore(double L, double W1, double W2)
        {
            // 使用加权平均的方式计算综合评分
            // 权重可以根据实际情况调整
            double lengthWeight = 0.4;
            double w1Weight = 0.3;
            double w2Weight = 0.3;

            return L * lengthWeight + W1 * w1Weight + W2 * w2Weight;
        }
        /// <summary>
        /// 根据叶片尺寸分类读取对应的路径文件
        /// </summary>
        /// <param name="bladeSize">叶片尺寸分类</param>
        /// <returns>路径文件内容</returns>
        private string ReadPathFile(BladeSize bladeSize)
        {
            string fileName = GetPathFileName(bladeSize);
            string filePath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ScanPath", fileName);

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"路径文件不存在：{filePath}");
            }

            return File.ReadAllText(filePath, Encoding.UTF8);
        }

        /// <summary>
        /// 根据叶片尺寸分类获取对应的文件名
        /// </summary>
        /// <param name="bladeSize">叶片尺寸分类</param>
        /// <returns>文件名</returns>
        private string GetPathFileName(BladeSize bladeSize)
        {
            switch (bladeSize)
            {
                case BladeSize.Small:
                    return "minPath.txt";
                case BladeSize.Medium:
                    return "mediumPath.txt";
                case BladeSize.Large:
                    return "maxPath.txt";
                default:
                    throw new ArgumentException($"未知的叶片尺寸分类：{bladeSize}");
            }
        }

        /// <summary>
        /// 显示分类结果和路径内容
        /// </summary>
        /// <param name="L">通道面长度</param>
        /// <param name="W1">叶片缘板最大宽度</param>
        /// <param name="W2">叶冠最大宽度</param>
        /// <param name="bladeSize">叶片尺寸分类</param>
        /// <param name="pathContent">路径文件内容</param>
        private void DisplayResult(double L, double W1, double W2, BladeSize bladeSize, string pathContent)
        {
            var sb = new StringBuilder();

            // 添加分析信息
            sb.AppendLine("; ==================== 叶片尺寸分析结果 ====================");
            sb.AppendLine($"; 分析时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"; 输入参数:");
            sb.AppendLine($";   L (通道面长度): {L:F2} mm");
            sb.AppendLine($";   W1 (叶片缘板最大宽度): {W1:F2} mm");
            sb.AppendLine($";   W2 (叶冠最大宽度): {W2:F2} mm");
            sb.AppendLine($"; 综合评分: {CalculateSizeScore(L, W1, W2):F2}");
            sb.AppendLine($"; 叶片尺寸分类: {GetSizeDisplayName(bladeSize)}");
            sb.AppendLine($"; 使用路径文件: {GetPathFileName(bladeSize)}");
            sb.AppendLine("; ========================================================");
            sb.AppendLine();

            // 添加路径文件内容
            sb.AppendLine("; 路径文件内容:");
            sb.AppendLine(pathContent);

            TxtOutput.Text = sb.ToString();
        }

        /// <summary>
        /// 获取叶片尺寸分类的显示名称
        /// </summary>
        /// <param name="bladeSize">叶片尺寸分类</param>
        /// <returns>显示名称</returns>
        private string GetSizeDisplayName(BladeSize bladeSize)
        {
            switch (bladeSize)
            {
                case BladeSize.Small:
                    return "小型叶片";
                case BladeSize.Medium:
                    return "中型叶片";
                case BladeSize.Large:
                    return "大型叶片";
                default:
                    return "未知";
            }
        }
    }
}