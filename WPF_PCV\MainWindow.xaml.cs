﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Remoting.Messaging;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using WPF_PCV.ViewModels;

namespace WPF_PCV
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        // 页面实例缓存：Type → Page
        private static readonly Dictionary<Type, Page> bufferedPages
            = new Dictionary<Type, Page>();

        public MainWindow()
        {
            InitializeComponent();
        }
        private void navMenu_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 1. 获取选中的 NavigationItem
            if (navMenu.SelectedItem is not NavigationItem item)
                return;

            // 2. 目标页面类型
            Type type = item.TargetPageType;

            // 3. 从缓存取或新建
#nullable enable
            if (!bufferedPages.TryGetValue(type, out Page? page))
            {
                page = Activator.CreateInstance(type) as Page
                       ?? throw new InvalidOperationException("无法创建页面实例");
                bufferedPages[type] = page;
            }

            // 4. 导航
            appFrame.Navigate(page);
        }
    }
}
