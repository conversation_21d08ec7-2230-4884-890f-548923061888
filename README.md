# README.md

一个使用 WPF（Windows Presentation Foundation）开发的桌面应用程序，支持点云加载、显示、采样、可视化切片等功能。项目集成了 [HelixToolkit.Wpf.SharpDX](https://github.com/helix-toolkit/helix-toolkit) 和 [OxyPlot](https://oxyplot.github.io/) 等图形可视化组件，适合于 3D 图形处理、计算机视觉、工程教学与科研应用等场景。

点云查看模块效果如图![](./Assets/Images/show.gif)
参数提取页面效果如图![](./Assets/Images/display.gif)

## `parameter.xaml.cs`的功能逻辑图

```mermaid
graph TD
    A[用户点击加载文件] --> B[OpenFile_Click]
    B --> C[选择文件对话框]
    C --> D{文件选择成功?}
    D -->|是| E[LoadPointCloudFileAsync]
    D -->|否| Z[结束]
    
    E --> F[ResetRenderingState]
    F --> G[ParseFileAsync - 异步解析]
    G --> H[CalculateGlobalTransform - 计算全局变换]
    H --> I[StartStreamingRender - 开始流式渲染]
    
    I --> J[RenderFirstChunk - 渲染首块]
    J --> K[ApplyGlobalTransform]
    K --> L[CreatePointCloudVisualizationVector3]
    L --> M[ContinueStreamingRender - 继续流式渲染]
    
    M --> N{还有数据块?}
    N -->|是| O[RenderNextChunk]
    O --> P[ApplyGlobalTransform]
    P --> Q[AddPointsToVisualization]
    Q --> N
    N -->|否| R[OnStreamingComplete - 流式完成]
    
    R --> S[AdjustCameraToFitPointCloudPublic]
    S --> T[ApplyDownsamplingAndRefresh]
    T --> U[BuildSliceDataInBackground - 后台构建切片]
    
    U --> V[DetectSliceAxis - 轴向检测]
    V --> W{检测结果}
    W -->|Z轴分层| X[ExtractUniqueLevels - Z轴]
    W -->|Y轴分层| Y[ExtractUniqueLevels - Y轴]
    
    X --> AA[BuildSlicesFromLevels - Z轴切片]
    Y --> BB[BuildSlicesFromLevels - Y轴切片]
    AA --> CC[ApplySliceDataToUI]
    BB --> CC
    
    CC --> DD[UpdateSliceView]
    CC --> EE[PopulateSectionListBox]
    DD --> FF[CreateSliceScatterSeries]
    EE --> GG[显示切片列表]
    
    %% 用户交互流程
    HH[用户双击切片列表] --> II[SectionListBox_MouseDoubleClick]
    II --> JJ[Update3DViewWithSelectedLayer]
    JJ --> KK[CreateSelectedLayerVisual - 红色高亮]
    
    LL[用户点击上一个切片] --> MM[BtnPrevSlice_Click]
    NN[用户点击下一个切片] --> OO[BtnNextSlice_Click]
    MM --> PP[NavigateToSlice]
    OO --> PP
    PP --> DD
    PP --> JJ
    
    %% 降采样控制
    QQ[用户调整降采样级别] --> RR[DownsampleLevelUpDown_ValueChanged]
    RR --> SS[ApplyDownsamplingAndRefresh]
    SS --> TT[VoxelGridDownsample]
    TT --> UU[更新3D显示]
    
    %% 异步处理流程
    subgraph "异步处理"
        G
        U
        I
        M
    end
    
    %% 数据流向
    subgraph "数据模型"
        VV[fullPoints - 完整数据]
        WW[renderPoints - 渲染数据]
        XX[slices2D - 2D切片]
        YY[sliceLevels - 层级数据]
    end
    
    G --> VV
    TT --> WW
    AA --> XX
    BB --> XX
    X --> YY
    Y --> YY
    
    style A fill:#e1f5fe
    style U fill:#fff3e0
    style V fill:#f3e5f5
    style I fill:#e8f5e8
    style CC fill:#fff8e1
```

## 📦 功能特性

- ✅ 加载本地 `.txt` 点云数据
- ✅ 支持点云降采样（体素网格法、随机等）
- ✅ 支持点云的 2D 和 3D 可视化显示
- ✅ 支持图像切片展示（基于投影平面）
- ✅ HelixToolkit 进行 3D 交互展示
- ✅ OxyPlot 显示统计曲线和切片图

## 🛠️ 技术栈

- 框架：WPF (.NET Framework 4.8)
- 渲染：HelixToolkit.Wpf.SharpDX, OxyPlot.Wpf
- 开发语言：C#
- 项目管理：Visual Studio 2022

## 📁 项目结构说明

``` markdown
WPF_PCV/
│  App.xaml             # 应用程序入口
│  index.xaml           # 主窗口界面
│  index.xaml.cs        # 主窗口逻辑
│  packages.config      # NuGet 包配置
│  WPF_PCV.csproj       # 项目文件
│
├─Properties/           # 项目属性设置
├─data4test/            # 示例数据文件夹（如 reduce.txt 等）
└─...                   # 其余代码与资源文件
```

## 🧰 环境依赖

请确保已安装以下环境：

- ✅ Visual Studio 2022（含 .NET 桌面开发工作负载）
- ✅ .NET Framework 4.8 开发包
- ✅ NuGet 包支持（VS 会自动还原）

## 🚀 使用方法

```shell
# 克隆项目
git clone https://github.com/get1024/WPF_PCV.git
cd WPF_PCV

# 在 Visual Studio 中打开 WPF_PCV.sln
# VS 会自动还原 packages.config 中定义的 NuGet 包

# 编译 & 运行（F5）
```

## 📝 数据格式说明

点云数据文件为 `.txt` 文本文件，每行为一个三维坐标点，格式如下：

```
-0.053	-8.215	50.000
-0.062	-8.199	50.000
-0.078	-8.174	50.000
-0.087	-8.159	35.000
-0.101	-8.136	35.000
-0.109	-8.121	35.000
-0.113	-8.113	35.000
...
```

## 💡 TODO

- [x] ~~降采样算法还有待优化，目前降采样效果不尽人意~~；
- [x] ~~分层算法需要调整，解决偶发出现的多层粘连问题~~；
- [ ] 把点云用真实坐标呈现；
- ……

## 📄 License

本项目代码暂未设置开源协议（默认为版权所有）。如需商业使用或二次开发，请联系作者。

## ✨ 作者

* 👤 [RyanJoy](https://github.com/get1024)