﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Cyotek.Drawing.BitmapFont" version="2.0.4" targetFramework="net48" />
  <package id="Extended.Wpf.Toolkit" version="4.7.25104.5739" targetFramework="net48" />
  <package id="HelixToolkit" version="2.27.0" targetFramework="net48" />
  <package id="HelixToolkit.Wpf.SharpDX" version="2.27.0" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="10.0.0-preview.6.25358.103" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="10.0.0-preview.6.25358.103" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="10.0.0-preview.6.25358.103" targetFramework="net48" />
  <package id="Microsoft.Net.Compilers" version="4.2.0" targetFramework="net48" developmentDependency="true" />
  <package id="Microsoft.NETFramework.ReferenceAssemblies" version="1.0.3" targetFramework="net48" developmentDependency="true" />
  <package id="Microsoft.NETFramework.ReferenceAssemblies.net48" version="1.0.3" targetFramework="net48" developmentDependency="true" />
  <package id="OxyPlot.Core" version="2.2.0" targetFramework="net48" />
  <package id="OxyPlot.Wpf" version="2.2.0" targetFramework="net48" />
  <package id="OxyPlot.Wpf.Shared" version="2.2.0" targetFramework="net48" />
  <package id="PclSharp" version="1.8.1.20180820-beta07" targetFramework="net48" />
  <package id="SharpDX" version="4.2.0" targetFramework="net48" />
  <package id="SharpDX.D3DCompiler" version="4.2.0" targetFramework="net48" />
  <package id="SharpDX.Direct2D1" version="4.2.0" targetFramework="net48" />
  <package id="SharpDX.Direct3D11" version="4.2.0" targetFramework="net48" />
  <package id="SharpDX.Direct3D11.Effects" version="4.2.0" targetFramework="net48" />
  <package id="SharpDX.Direct3D9" version="4.2.0" targetFramework="net48" />
  <package id="SharpDX.DXGI" version="4.2.0" targetFramework="net48" />
  <package id="SharpDX.Mathematics" version="4.2.0" targetFramework="net48" />
  <package id="System.Buffers" version="4.6.1" targetFramework="net48" />
  <package id="System.Collections.NonGeneric" version="4.3.0" targetFramework="net48" />
  <package id="System.ComponentModel.Primitives" version="4.3.0" targetFramework="net48" />
  <package id="System.ComponentModel.TypeConverter" version="4.3.0" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="10.0.0-preview.6.25358.103" targetFramework="net48" />
  <package id="System.Drawing.Primitives" version="4.3.0" targetFramework="net48" />
  <package id="System.Memory" version="4.6.3" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.6.1" targetFramework="net48" />
  <package id="System.Private.DataContractSerialization" version="4.3.0" targetFramework="net48" />
  <package id="System.Reflection" version="4.3.0" targetFramework="net48" />
  <package id="System.Reflection.TypeExtensions" version="4.7.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.1.2" targetFramework="net48" />
  <package id="System.Runtime.Serialization.Primitives" version="4.3.0" targetFramework="net48" />
  <package id="System.Runtime.Serialization.Xml" version="4.3.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.6.3" targetFramework="net48" />
  <package id="System.Threading.Tasks.Parallel" version="4.3.0" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.6.1" targetFramework="net48" />
  <package id="System.Xml.XmlDocument" version="4.3.0" targetFramework="net48" />
  <package id="WindowsAPICodePack-Core" version="1.1.2" targetFramework="net48" />
  <package id="WindowsAPICodePack-Shell" version="1.1.1" targetFramework="net48" />
</packages>