﻿<Window x:Class="WPF_PCV.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WPF_PCV"
        xmlns:pages="clr-namespace:WPF_PCV.Views.Pages"
        xmlns:vm="clr-namespace:WPF_PCV.ViewModels"
        mc:Ignorable="d"
        WindowStartupLocation="CenterScreen"
        Title="MainWindow" Height="650" Width="1150">
    <Window.DataContext>
        <vm:MainWindowViewModel/>
    </Window.DataContext>
        
    <Grid ShowGridLines="True">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="20*"/>
        </Grid.ColumnDefinitions>

        <ListBox Name="navMenu"
             ItemsSource="{Binding NavigationItems}" BorderThickness="0 0 1 0"
             ScrollViewer.HorizontalScrollBarVisibility="Disabled"
             SelectionChanged="navMenu_SelectionChanged" Grid.Column="0">
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <Border Padding="5">
                        <StackPanel>
                            <TextBlock Text="{Binding Title}"/>
                            <!--描述信息用灰色字体, 字号也小一点, 多余的部分省略号-->
                            <TextBlock Text="{Binding Description}" Foreground="Gray"
                            FontSize="10" TextTrimming="CharacterEllipsis"/>
                        </StackPanel>
                    </Border>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>

        <Frame Grid.Column="1" x:Name="appFrame" NavigationUIVisibility="Hidden"/>
    </Grid>
</Window>
