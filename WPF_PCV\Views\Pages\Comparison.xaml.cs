﻿using Microsoft.Win32;
using Microsoft.WindowsAPICodePack.Dialogs;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

//namespace WPF_PCV.Views.Pages
//{
//    /// <summary>
//    /// Comparison.xaml 的交互逻辑
//    /// </summary>
//    public partial class Comparison : Page
//    {
//        // 定义常量路径
//        private const string REPORT_EXE_RELATIVE_PATH = "pyFunc/Comparison/report.exe";
//        private const string REPORT_OUTPUT_RELATIVE_PATH = "pyFunc/Comparison/ReportOutput";

//        public Comparison()
//        {
//            InitializeComponent();
//        }

//        private void ChooseFile_Click(object sender, RoutedEventArgs e)
//        {
//            var dlg = new OpenFileDialog
//            {
//                Title = "选择注册源文件",
//                Filter = "所有文件 (*.*)|*.*"
//            };
//            if (dlg.ShowDialog() == true)
//                FilePath.Text = dlg.FileName;
//        }

//        // 选择 CMM 数据文件夹（选择文件夹）
//        private void ChooseFolder_Click(object sender, RoutedEventArgs e)
//        {
//            var dialog = new CommonOpenFileDialog();
//            dialog.Title = "请选择cmm文件夹";
//            dialog.IsFolderPicker = true;
//            if (dialog.ShowDialog() == CommonFileDialogResult.Ok)
//            {
//                FolderPath.Text = dialog.FileName;
//            }
//        }

//        /// <summary>
//        /// 获取项目根目录的健壮方法
//        /// </summary>
//        /// <returns>项目根目录的绝对路径</returns>
//        private string GetProjectRootDirectory()
//        {
//            // 方法1：从当前可执行文件位置开始查找
//            string currentDir = AppDomain.CurrentDomain.BaseDirectory;

//            // 向上查找直到找到包含 pyFunc 目录的文件夹
//            while (!string.IsNullOrEmpty(currentDir))
//            {
//                string pyFuncPath = System.IO.Path.Combine(currentDir, "pyFunc");
//                if (Directory.Exists(pyFuncPath))
//                {
//                    return currentDir;
//                }

//                // 向上级目录查找
//                string parentDir = System.IO.Path.GetDirectoryName(currentDir);
//                if (parentDir == currentDir) // 已经到达根目录
//                    break;
//                currentDir = parentDir;
//            }

//            // 方法2：如果方法1失败，尝试从解决方案文件位置查找
//            string solutionPath = FindSolutionFile();
//            if (!string.IsNullOrEmpty(solutionPath))
//            {
//                return System.IO.Path.GetDirectoryName(solutionPath);
//            }
//            throw new DirectoryNotFoundException("无法找到项目根目录，请确保 pyFunc 目录存在");
//        }

//        /// <summary>
//        /// 查找解决方案文件
//        /// </summary>
//        /// <returns>解决方案文件的路径</returns>
//        private string FindSolutionFile()
//        {
//            string currentDir = AppDomain.CurrentDomain.BaseDirectory;

//            while (!string.IsNullOrEmpty(currentDir))
//            {
//                string[] slnFiles = Directory.GetFiles(currentDir, "*.sln");
//                if (slnFiles.Length > 0)
//                {
//                    return slnFiles[0];
//                }
//                string parentDir = System.IO.Path.GetDirectoryName(currentDir);
//                if (parentDir == currentDir)
//                    break;
//                currentDir = parentDir;
//            }
//            return null;
//        }

//        /// <summary>
//        /// 验证并获取 report.exe 的完整路径
//        /// </summary>
//        /// <returns>report.exe 的完整路径</returns>
//        private string GetReportExePath()
//        {
//            string projectRoot = GetProjectRootDirectory();
//            string exePath = System.IO.Path.Combine(projectRoot, REPORT_EXE_RELATIVE_PATH);

//            if (!File.Exists(exePath))
//            {
//                throw new FileNotFoundException($"未找到 report.exe 文件: {exePath}");
//            }
//            return exePath;
//        }

//        /// <summary>
//        /// 获取报告输出目录
//        /// </summary>
//        /// <returns>报告输出目录的完整路径</returns>
//        private string GetReportOutputDirectory()
//        {
//            string projectRoot = GetProjectRootDirectory();
//            string outputPath = System.IO.Path.Combine(projectRoot, REPORT_OUTPUT_RELATIVE_PATH);

//            // 确保输出目录存在
//            if (!Directory.Exists(outputPath))
//            {
//                Directory.CreateDirectory(outputPath);
//            }
//            return outputPath;
//        }

//        private async void OutputPDF_Click(object sender, RoutedEventArgs e)
//        {
//            if (string.IsNullOrEmpty(FilePath.Text) || string.IsNullOrEmpty(FolderPath.Text))
//            {
//                MessageBox.Show("请先选择注册源文件和 CMM 数据文件夹！", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
//                return;
//            }

//            OutputPDF.IsEnabled = false;
//            PbReportProgress.Value = 0;
//            TbProgressInfo.Text = "开始生成…";

//            try
//            {
//                // 获取路径
//                string reportExePath = GetReportExePath();
//                string reportOutputDir = GetReportOutputDirectory();
//                string regSourcePath = FilePath.Text;
//                string cmmFolder = FolderPath.Text;

//                // 验证输入文件是否存在
//                if (!File.Exists(regSourcePath))
//                {
//                    MessageBox.Show($"注册源文件不存在: {regSourcePath}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
//                    return;
//                }

//                if (!Directory.Exists(cmmFolder))
//                {
//                    MessageBox.Show($"CMM 数据文件夹不存在: {cmmFolder}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
//                    return;
//                }

//                // 构造进程启动信息
//                var psi = new ProcessStartInfo
//                {
//                    FileName = reportExePath,
//                    Arguments = $"\"{regSourcePath}\" \"{cmmFolder}\"",
//                    WorkingDirectory = System.IO.Path.GetDirectoryName(reportExePath), // 设置工作目录为exe所在目录
//                    RedirectStandardOutput = true,
//                    RedirectStandardError = true,
//                    UseShellExecute = false,
//                    CreateNoWindow = true
//                };

//                var proc = new Process { StartInfo = psi, EnableRaisingEvents = true };
//                int fileCount = 13, processed = 0;

//                proc.OutputDataReceived += (s, ea) =>
//                {
//                    if (ea.Data == null) return;
//                    Dispatcher.Invoke(() =>
//                    {
//                        if (ea.Data.StartsWith("PROGRESS:"))
//                        {
//                            processed++;
//                            PbReportProgress.Value = (double)processed / fileCount * 100;

//                            // 取出真正要显示的文字
//                            string msg = ea.Data.Substring("PROGRESS:".Length);

//                            // 把它加在现有文本的最前面，每条独占一行
//                            TbProgressInfo.Text = msg
//                                                 + Environment.NewLine
//                                                 + TbProgressInfo.Text;
//                        }
//                    });
//                };

//                proc.ErrorDataReceived += (s, ea) =>
//                {
//                    if (ea.Data != null)
//                        Dispatcher.Invoke(() => TbProgressInfo.Text = $"Error: {ea.Data}" + Environment.NewLine + TbProgressInfo.Text);
//                };

//                proc.Start();
//                proc.BeginOutputReadLine();
//                proc.BeginErrorReadLine();
//                await Task.Run(() => proc.WaitForExit());

//                if (proc.ExitCode == 0)
//                {
//                    string normalizedOutputDir = NormalizePath(reportOutputDir);
//                    string latestPdf = GetLatestPdfFile(reportOutputDir);
//                    string normalizedPdf = latestPdf != null ? NormalizePath(latestPdf) : null;
//                    // 使用XAML弹窗
//                    var dlg = new SuccessDialog($"PDF 报告已成功生成！\n输出位置: {normalizedPdf}", normalizedPdf, reportOutputDir);
//                    // 设置 SuccessDialog 出现的位置
//                    dlg.Owner = Window.GetWindow(this);
//                    dlg.WindowStartupLocation = WindowStartupLocation.CenterOwner;
//                    dlg.ShowDialog();
//                }
//                else
//                {
//                    MessageBox.Show($"生成 PDF 报告失败\nExitCode={proc.ExitCode}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
//                }
//            }
//            catch (FileNotFoundException ex)
//            {
//                MessageBox.Show($"文件未找到: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
//            }
//            catch (DirectoryNotFoundException ex)
//            {
//                MessageBox.Show($"目录未找到: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
//            }
//            catch (Exception ex)
//            {
//                MessageBox.Show($"执行过程中发生异常:\n{ex.Message}", "异常", MessageBoxButton.OK, MessageBoxImage.Error);
//            }
//            finally
//            {
//                OutputPDF.IsEnabled = true;
//            }
//        }

//        private string NormalizePath(string path)
//        {
//            return System.IO.Path.GetFullPath(path).Replace('/', '\\');
//        }

//        private string GetLatestPdfFile(string outputDir)
//        {
//            // 获取 outputDir 目录下所有扩展名为 .pdf 的文件，返回文件路径数组
//            var pdfFiles = Directory.GetFiles(outputDir, "*.pdf");

//            // 如果没有找到任何 PDF 文件，返回 null
//            if (pdfFiles.Length == 0) return null;

//            // 按文件的最后修改时间降序排序，取第一个（也就是最新的那个），并返回其路径
//            return pdfFiles.OrderByDescending(f => File.GetLastWriteTime(f)).First();
//        }
//    }
//}

namespace WPF_PCV.Views.Pages
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class Comparison : Page
    {
        public Comparison()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 选择文件按钮点击事件
        /// </summary>
        private void ChooseFile_Click(object sender, RoutedEventArgs e)
        {
            var dlg = new OpenFileDialog
            {
                Title = "选择注册源文件",
                Filter = "所有文件 (*.*)|*.*"
            };
            if (dlg.ShowDialog() == true)
                FilePath.Text = dlg.FileName;
        }

        /// <summary>
        /// 选择文件夹按钮点击事件
        /// </summary>
        private void ChooseFolder_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new CommonOpenFileDialog();
            dialog.Title = "请选择cmm文件夹";
            dialog.IsFolderPicker = true;
            if (dialog.ShowDialog() == CommonFileDialogResult.Ok)
            {
                FolderPath.Text = dialog.FileName;
            }
        }

        /// <summary>
        /// 生成PDF报告按钮点击事件
        /// </summary>
        private async void OutputPDF_Click(object sender, RoutedEventArgs e)
        {
            // 验证输入
            if (string.IsNullOrWhiteSpace(FilePath.Text))
            {
                System.Windows.MessageBox.Show("请先选择截面带数据文件！", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(FolderPath.Text))
            {
                System.Windows.MessageBox.Show("请先选择CMM数据文件夹！", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (!File.Exists(FilePath.Text))
            {
                System.Windows.MessageBox.Show("选择的文件不存在！", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (!Directory.Exists(FolderPath.Text))
            {
                System.Windows.MessageBox.Show("选择的文件夹不存在！", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // 禁用按钮，防止重复点击
            OutputPDF.IsEnabled = false;
            PbReportProgress.Value = 0;
            TbProgressInfo.Text = "开始生成报告...\n";

            try
            {
                await RunPythonScript(FilePath.Text, FolderPath.Text);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"生成报告时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                TbProgressInfo.Text += $"错误：{ex.Message}\n";
            }
            finally
            {
                // 重新启用按钮
                OutputPDF.IsEnabled = true;
                PbReportProgress.Value = 100;
            }
        }

        /// <summary>
        /// 修复编码问题
        /// </summary>
        private string FixEncoding(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            try
            {
                // 如果字符串看起来已经是正确的UTF-8，直接返回
                if (!input.Contains("�") && !HasEncodingIssues(input))
                {
                    return input;
                }

                // 尝试从Latin-1转换为UTF-8（常见的编码问题）
                byte[] bytes = Encoding.GetEncoding("ISO-8859-1").GetBytes(input);
                string result = Encoding.UTF8.GetString(bytes);

                if (!result.Contains("�") && result.Length > 0)
                {
                    return result;
                }

                // 如果转换失败，返回原始字符串
                return input;
            }
            catch
            {
                // 如果转换失败，返回原始字符串
                return input;
            }
        }

        /// <summary>
        /// 检查字符串是否有编码问题
        /// </summary>
        private bool HasEncodingIssues(string input)
        {
            // 检查是否包含常见的编码问题字符
            return input.Contains("ï¿½") || input.Contains("Ã") || input.Contains("â");
        }

        /// <summary>
        /// 查找项目根目录
        /// </summary>
        private string FindProjectRoot(string startPath)
        {
            string currentPath = startPath;

            // 向上查找，直到找到包含pyFunc目录的路径
            while (!string.IsNullOrEmpty(currentPath))
            {
                string pyFuncPath = System.IO.Path.Combine(currentPath, "pyFunc");
                if (Directory.Exists(pyFuncPath))
                {
                    return currentPath;
                }

                // 向上一级目录
                DirectoryInfo parentDir = Directory.GetParent(currentPath);
                if (parentDir == null)
                    break;
                currentPath = parentDir.FullName;
            }

            // 如果没找到，尝试使用相对路径（开发环境）
            string devPath = System.IO.Path.GetFullPath(System.IO.Path.Combine(startPath, "..", "..", "..", ".."));
            string devPyFuncPath = System.IO.Path.Combine(devPath, "pyFunc");
            if (Directory.Exists(devPyFuncPath))
            {
                return devPath;
            }

            // 最后尝试当前工作目录
            string workingDir = Environment.CurrentDirectory;
            string workingPyFuncPath = System.IO.Path.Combine(workingDir, "pyFunc");
            if (Directory.Exists(workingPyFuncPath))
            {
                return workingDir;
            }

            throw new DirectoryNotFoundException($"无法找到pyFunc目录。搜索路径：{startPath}");
        }

        /// <summary>
        /// 运行Python脚本
        /// </summary>
        private async Task RunPythonScript(string filePath, string folderPath)
        {
            await Task.Run(() =>
            {
                try
                {
                    // 获取项目根目录（从应用程序目录向上查找）
                    string appDirectory = AppDomain.CurrentDomain.BaseDirectory;
                    string projectRoot = FindProjectRoot(appDirectory);
                    string pyFuncPath = System.IO.Path.Combine(projectRoot, "pyFunc");
                    string pythonExePath = System.IO.Path.Combine(pyFuncPath, ".venv", "Scripts", "python.exe");
                    string scriptPath = System.IO.Path.Combine(pyFuncPath, "comparison", "report.py");

                    // 检查Python可执行文件是否存在
                    if (!File.Exists(pythonExePath))
                    {
                        throw new FileNotFoundException($"Python可执行文件不存在：{pythonExePath}");
                    }

                    // 检查脚本文件是否存在
                    if (!File.Exists(scriptPath))
                    {
                        throw new FileNotFoundException($"Python脚本不存在：{scriptPath}");
                    }

                    // 更新UI（需要在UI线程中执行）
                    Dispatcher.Invoke(() =>
                    {
                        PbReportProgress.Value = 10;
                        TbProgressInfo.Text += "正在启动Python脚本...\n";
                    });

                    // 创建进程启动信息
                    string comparisonPath = System.IO.Path.Combine(pyFuncPath, "comparison");
                    ProcessStartInfo startInfo = new ProcessStartInfo
                    {
                        FileName = pythonExePath,
                        Arguments = $"\"{scriptPath}\" \"{filePath}\" \"{folderPath}\"",
                        WorkingDirectory = comparisonPath,  // 设置工作目录为comparison
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true,
                        StandardOutputEncoding = Encoding.UTF8, // 使用UTF8编码
                        StandardErrorEncoding = Encoding.UTF8   // 使用UTF8编码
                    };

                    // 设置环境变量确保Python输出UTF-8
                    startInfo.EnvironmentVariables["PYTHONIOENCODING"] = "utf-8";
                    startInfo.EnvironmentVariables["PYTHONLEGACYWINDOWSSTDIO"] = "0";
                    startInfo.EnvironmentVariables["LANG"] = "zh_CN.UTF-8";

                    // 启动进程
                    using (Process process = new Process())
                    {
                        process.StartInfo = startInfo;

                        // 处理输出
                        process.OutputDataReceived += (sender, e) =>
                        {
                            if (!string.IsNullOrEmpty(e.Data))
                            {
                                Dispatcher.Invoke(() =>
                                {
                                    // 尝试修复编码问题
                                    string decodedData = FixEncoding(e.Data);
                                    TbProgressInfo.Text += decodedData + "\n";
                                    TbProgressInfo.ScrollToEnd();

                                    // 根据输出更新进度条
                                    if (decodedData.Contains("PROGRESS:"))
                                    {
                                        PbReportProgress.Value = Math.Min(PbReportProgress.Value + 15, 90);
                                    }
                                });
                            }
                        };

                        process.ErrorDataReceived += (sender, e) =>
                        {
                            if (!string.IsNullOrEmpty(e.Data))
                            {
                                Dispatcher.Invoke(() =>
                                {
                                    // 尝试修复编码问题
                                    string decodedData = FixEncoding(e.Data);
                                    TbProgressInfo.Text += $"错误：{decodedData}\n";
                                    TbProgressInfo.ScrollToEnd();
                                });
                            }
                        };

                        process.Start();
                        process.BeginOutputReadLine();
                        process.BeginErrorReadLine();

                        // 等待进程完成
                        process.WaitForExit();

                        Dispatcher.Invoke(() =>
                        {
                            if (process.ExitCode == 0)
                            {
                                TbProgressInfo.Text += "报告生成完成！\n";
                                PbReportProgress.Value = 100;
                            }
                            else
                            {
                                TbProgressInfo.Text += $"Python脚本执行失败，退出代码：{process.ExitCode}\n";
                            }
                        });
                    }
                }
                catch (Exception ex)
                {
                    Dispatcher.Invoke(() =>
                    {
                        TbProgressInfo.Text += $"执行Python脚本时发生错误：{ex.Message}\n";
                    });
                    throw;
                }
            });
        }
    }
}